// 内容脚本 - 在网页中运行，用于操作页面元素

// 监听来自弹窗的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'ping') {
        sendResponse({status: 'ready'});
        return true;
    }
    
    if (request.action === 'clickTargetButton') {
        try {
            // 查找目标按钮元素
            const targetButton = findTargetButton();
            
            if (targetButton) {
                // 模拟点击事件
                clickElement(targetButton);
                sendResponse({success: true, message: '按钮点击成功'});
            } else {
                sendResponse({success: false, message: '未找到目标按钮元素'});
            }
        } catch (error) {
            console.error('点击按钮时出错:', error);
            sendResponse({success: false, message: '点击失败: ' + error.message});
        }
        return true; // 保持消息通道开放
    }
});

// 查找目标按钮元素
function findTargetButton() {
    // 方法1: 使用完整的选择器
    let button = document.querySelector('li[role="option"][data-checked="true"][data-disabled="false"].cIL_item_5-118-0.cIL_medium_5-118-0.cIL_active_5-118-0.ST_itemRendererLabel_5-118-0.ST_itemRendererLabelChecked_5-118-0 span');
    
    if (button) {
        return button.closest('li'); // 返回li元素而不是span
    }
    
    // 方法2: 更宽松的选择器，查找包含"100"文本的元素
    const allLiElements = document.querySelectorAll('li[role="option"]');
    for (let li of allLiElements) {
        const span = li.querySelector('span');
        if (span && span.textContent.trim() === '100') {
            return li;
        }
    }
    
    // 方法3: 查找所有包含"100"文本的span，然后找到其父级li
    const allSpans = document.querySelectorAll('span');
    for (let span of allSpans) {
        if (span.textContent.trim() === '100') {
            const parentLi = span.closest('li[role="option"]');
            if (parentLi) {
                return parentLi;
            }
        }
    }
    
    // 方法4: 使用部分类名匹配
    const itemElements = document.querySelectorAll('li[role="option"][data-checked="true"]');
    for (let item of itemElements) {
        const span = item.querySelector('span');
        if (span && span.textContent.trim() === '100') {
            return item;
        }
    }
    
    return null;
}

// 模拟真实的用户点击
function clickElement(element) {
    // 确保元素可见
    element.scrollIntoView({behavior: 'smooth', block: 'center'});
    
    // 等待一小段时间确保滚动完成
    setTimeout(() => {
        // 创建并触发多种点击事件以确保兼容性
        const events = ['mousedown', 'mouseup', 'click'];
        
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: element.getBoundingClientRect().left + element.offsetWidth / 2,
                clientY: element.getBoundingClientRect().top + element.offsetHeight / 2
            });
            element.dispatchEvent(event);
        });
        
        // 如果元素有onclick属性，也尝试调用
        if (element.onclick) {
            element.onclick();
        }
        
        // 触发focus事件
        if (element.focus) {
            element.focus();
        }
        
        console.log('目标按钮已被点击:', element);
    }, 100);
}

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

function initialize() {
    console.log('自动点击按钮插件内容脚本已加载');
    
    // 可以在这里添加页面监听器或其他初始化代码
    // 例如：监听页面变化，自动检测目标按钮等
}
