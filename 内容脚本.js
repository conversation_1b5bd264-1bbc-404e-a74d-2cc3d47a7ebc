// 内容脚本 - 在网页中运行，用于操作页面元素

// 监听来自弹窗的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'ping') {
        sendResponse({status: 'ready'});
        return true;
    }

    if (request.action === 'clickTargetButton') {
        try {
            // 执行自动下拉并点击100的操作
            performDropdownAction(sendResponse);
        } catch (error) {
            console.error('执行操作时出错:', error);
            sendResponse({success: false, message: '操作失败: ' + error.message});
        }
        return true; // 保持消息通道开放
    }
});

// 执行下拉菜单操作的主函数
async function performDropdownAction(sendResponse) {
    try {
        // 步骤1: 查找并点击下拉触发器
        const dropdownTrigger = findDropdownTrigger();
        if (!dropdownTrigger) {
            sendResponse({success: false, message: '未找到下拉菜单触发器'});
            return;
        }

        console.log('找到下拉触发器:', dropdownTrigger);

        // 点击触发器打开下拉菜单
        clickElement(dropdownTrigger);

        // 等待下拉菜单展开
        setTimeout(async () => {
            // 步骤2: 查找并点击"100"选项
            const targetOption = await findTargetOption();
            if (targetOption) {
                clickElement(targetOption);
                sendResponse({success: true, message: '成功点击100选项'});
            } else {
                sendResponse({success: false, message: '下拉菜单已打开，但未找到100选项'});
            }
        }, 300); // 等待300ms让下拉菜单完全展开

    } catch (error) {
        console.error('执行下拉操作时出错:', error);
        sendResponse({success: false, message: '操作失败: ' + error.message});
    }
}

// 查找下拉菜单触发器
function findDropdownTrigger() {
    // 方法1: 查找包含下拉面板的容器的触发器
    const portalMain = document.querySelector('[data-testid="beast-core-portal-main"]');
    if (portalMain) {
        // 查找触发器，通常在portal附近或者是其父元素
        const possibleTriggers = [
            portalMain.previousElementSibling,
            portalMain.parentElement?.querySelector('button'),
            portalMain.parentElement?.querySelector('[role="button"]'),
            portalMain.parentElement?.querySelector('.dropdown-trigger'),
            portalMain.parentElement?.querySelector('[aria-haspopup]'),
        ];

        for (let trigger of possibleTriggers) {
            if (trigger && trigger.offsetParent !== null) { // 确保元素可见
                return trigger;
            }
        }
    }

    // 方法2: 查找常见的下拉触发器
    const commonTriggers = [
        'button[aria-haspopup="listbox"]',
        'div[role="button"][aria-haspopup]',
        'button[aria-expanded]',
        'div[aria-expanded]',
        '.dropdown-toggle',
        '[data-toggle="dropdown"]'
    ];

    for (let selector of commonTriggers) {
        const trigger = document.querySelector(selector);
        if (trigger && trigger.offsetParent !== null) {
            return trigger;
        }
    }

    // 方法3: 查找包含当前选中值的元素（通常是触发器）
    const allElements = document.querySelectorAll('*');
    for (let element of allElements) {
        if (element.textContent && element.textContent.includes('100') &&
            !element.querySelector('ul[role="listbox"]') && // 不是下拉面板本身
            element.offsetParent !== null) {
            return element;
        }
    }

    return null;
}

// 查找目标选项（100）
async function findTargetOption() {
    // 等待下拉面板出现
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
        // 方法1: 在下拉面板中查找100选项
        const dropdownPanel = document.querySelector('ul[role="listbox"]');
        if (dropdownPanel) {
            const options = dropdownPanel.querySelectorAll('li[role="option"]');
            for (let option of options) {
                const span = option.querySelector('span');
                if (span && span.textContent.trim() === '100') {
                    return option;
                }
            }
        }

        // 方法2: 全局查找包含"100"的选项
        const allOptions = document.querySelectorAll('li[role="option"]');
        for (let option of allOptions) {
            const span = option.querySelector('span');
            if (span && span.textContent.trim() === '100' &&
                option.offsetParent !== null) { // 确保可见
                return option;
            }
        }

        attempts++;
        await new Promise(resolve => setTimeout(resolve, 100)); // 等待100ms
    }

    return null;
}

// 模拟真实的用户点击
function clickElement(element) {
    if (!element) {
        console.error('尝试点击空元素');
        return;
    }

    console.log('准备点击元素:', element);

    // 确保元素可见
    element.scrollIntoView({behavior: 'smooth', block: 'center'});

    // 获取元素位置
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 创建并触发完整的鼠标事件序列
    const eventTypes = ['mouseenter', 'mouseover', 'mousedown', 'mouseup', 'click'];

    eventTypes.forEach((eventType, index) => {
        setTimeout(() => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: centerX,
                clientY: centerY,
                button: 0,
                buttons: eventType === 'mousedown' ? 1 : 0
            });

            element.dispatchEvent(event);
            console.log(`触发事件: ${eventType}`);
        }, index * 10); // 每个事件间隔10ms
    });

    // 额外的兼容性处理
    setTimeout(() => {
        // 尝试触发focus事件
        if (element.focus && typeof element.focus === 'function') {
            element.focus();
        }

        // 如果是input或select元素，尝试触发change事件
        if (element.tagName === 'INPUT' || element.tagName === 'SELECT') {
            const changeEvent = new Event('change', { bubbles: true });
            element.dispatchEvent(changeEvent);
        }

        // 尝试调用onclick处理器
        if (element.onclick && typeof element.onclick === 'function') {
            element.onclick();
        }

        console.log('元素点击完成:', element);
    }, 100);
}

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

function initialize() {
    console.log('自动点击按钮插件内容脚本已加载');
    
    // 可以在这里添加页面监听器或其他初始化代码
    // 例如：监听页面变化，自动检测目标按钮等
}
