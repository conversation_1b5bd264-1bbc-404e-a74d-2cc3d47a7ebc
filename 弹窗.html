<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>自动点击按钮</title>
    <style>
      body {
        width: 300px;
        padding: 20px;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin: 0;
      }
      
      .container {
        text-align: center;
      }
      
      h1 {
        font-size: 18px;
        margin-bottom: 20px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }
      
      .button {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        font-weight: bold;
      }
      
      .button:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
      }
      
      .status {
        margin-top: 15px;
        font-size: 14px;
        min-height: 20px;
        opacity: 0.9;
      }
      
      .success {
        color: #4CAF50;
        font-weight: bold;
      }
      
      .error {
        color: #f44336;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>自动点击按钮插件</h1>
      <button id="clickButton" class="button">点击目标按钮</button>
      <div id="status" class="status"></div>
    </div>
    <script src="弹窗脚本.js"></script>
  </body>
</html>
