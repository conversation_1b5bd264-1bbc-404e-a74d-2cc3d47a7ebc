// 后台脚本 - Service Worker

// 插件安装时的处理
chrome.runtime.onInstalled.addListener(function(details) {
    console.log('自动点击按钮插件已安装');
    
    if (details.reason === 'install') {
        console.log('这是首次安装');
    } else if (details.reason === 'update') {
        console.log('插件已更新到版本:', chrome.runtime.getManifest().version);
    }
});

// 处理来自内容脚本或弹窗的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('后台脚本收到消息:', request);
    
    // 可以在这里添加更多的消息处理逻辑
    if (request.action === 'log') {
        console.log('来自内容脚本的日志:', request.message);
    }
    
    return true;
});

// 标签页更新时的处理
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // 当页面完全加载后，可以执行一些初始化操作
    if (changeInfo.status === 'complete' && tab.url) {
        console.log('页面加载完成:', tab.url);
    }
});

// 插件图标点击处理（如果需要的话）
chrome.action.onClicked.addListener(function(tab) {
    // 这个事件只有在manifest中没有定义default_popup时才会触发
    console.log('插件图标被点击');
});
