# 自动点击按钮插件 - 安装说明

## 功能介绍
这个谷歌浏览器插件可以自动点击页面上指定的按钮元素。具体目标是点击以下HTML结构的按钮：
```html
<li role="option" data-checked="true" data-disabled="false" class="cIL_item_5-118-0 cIL_medium_5-118-0 cIL_active_5-118-0 ST_itemRendererLabel_5-118-0 ST_itemRendererLabelChecked_5-118-0">
    <span>100</span>
</li>
```

## 安装步骤

### 1. 准备插件文件
确保以下文件都在同一个文件夹中：
- `manifest.json` - 插件配置文件
- `弹窗.html` - 插件弹窗界面
- `弹窗脚本.js` - 弹窗逻辑脚本
- `内容脚本.js` - 页面操作脚本
- `后台脚本.js` - 后台服务脚本

### 2. 在Chrome中加载插件
1. 打开谷歌浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成后会出现在扩展程序列表中

### 3. 使用插件
1. 导航到包含目标按钮的网页
2. 点击浏览器工具栏中的插件图标
3. 在弹出的窗口中点击"点击目标按钮"
4. 插件会自动查找并点击页面上的目标按钮

## 插件特性

### 智能查找
插件使用多种方法查找目标按钮：
1. 完整CSS选择器匹配
2. 基于文本内容("100")的查找
3. 部分类名匹配
4. 灵活的DOM结构适配

### 真实点击模拟
- 模拟真实的鼠标点击事件
- 包含mousedown、mouseup、click事件序列
- 自动滚动到目标元素位置
- 触发focus事件确保元素激活

### 用户友好界面
- 美观的渐变背景设计
- 清晰的状态反馈
- 成功/失败消息提示
- 响应式按钮交互效果

## 故障排除

### 如果插件无法找到按钮：
1. 确保页面已完全加载
2. 检查目标按钮是否存在于页面中
3. 刷新页面后重试
4. 查看浏览器控制台是否有错误信息

### 如果点击无效：
1. 目标元素可能被其他元素遮挡
2. 页面可能使用了特殊的事件处理机制
3. 可能需要等待页面JavaScript完全加载

### 调试方法：
1. 按F12打开开发者工具
2. 在Console标签页查看插件日志
3. 检查是否有JavaScript错误

## 技术说明

### 文件结构：
- **manifest.json**: 定义插件权限和配置
- **弹窗.html/弹窗脚本.js**: 用户界面和交互逻辑
- **内容脚本.js**: 在网页中运行，负责查找和点击按钮
- **后台脚本.js**: 后台服务，处理插件生命周期

### 权限说明：
- `activeTab`: 访问当前活动标签页
- `scripting`: 在页面中注入和执行脚本

## 版本信息
- 版本：1.0
- 兼容性：Chrome Manifest V3
- 开发语言：JavaScript, HTML, CSS
