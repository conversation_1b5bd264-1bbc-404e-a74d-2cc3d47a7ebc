# 自动下拉选择插件 - 安装说明

## 功能介绍
这个谷歌浏览器插件可以自动打开下拉菜单并选择指定选项。具体功能：
1. **自动触发下拉菜单**：智能查找并点击下拉触发器
2. **自动选择选项**：在下拉菜单中找到并点击"100"选项

目标HTML结构：
```html
<div data-testid="beast-core-portal-main" class="PT_portalMain_5-118-0 PP_dropdownMain_5-118-0 ST_dropdownMain_5-118-0">
  <div style="display: flex;">
    <div style="flex: 1 1 auto; width: 100%;">
      <div class="">
        <ul role="listbox" class="ST_dropdownPanel_5-118-0">
          <li role="option" data-checked="false" data-disabled="false" class="cIL_item_5-118-0 cIL_medium_5-118-0 ST_itemRendererLabel_5-118-0">
            <span>10</span>
          </li>
          <li role="option" data-checked="false" data-disabled="false" class="cIL_item_5-118-0 cIL_medium_5-118-0 ST_itemRendererLabel_5-118-0">
            <span>20</span>
          </li>
          <li role="option" data-checked="false" data-disabled="false" class="cIL_item_5-118-0 cIL_medium_5-118-0 ST_itemRendererLabel_5-118-0">
            <span>40</span>
          </li>
          <li role="option" data-checked="true" data-disabled="false" class="cIL_item_5-118-0 cIL_medium_5-118-0 cIL_active_5-118-0 ST_itemRendererLabel_5-118-0 ST_itemRendererLabelChecked_5-118-0">
            <span>100</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
```

## 安装步骤

### 1. 准备插件文件
确保以下文件都在同一个文件夹中：
- `manifest.json` - 插件配置文件
- `弹窗.html` - 插件弹窗界面
- `弹窗脚本.js` - 弹窗逻辑脚本
- `内容脚本.js` - 页面操作脚本
- `后台脚本.js` - 后台服务脚本

### 2. 在Chrome中加载插件
1. 打开谷歌浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成后会出现在扩展程序列表中

### 3. 使用插件
1. 导航到包含目标下拉菜单的网页
2. 点击浏览器工具栏中的插件图标
3. 在弹出的窗口中点击"打开下拉并选择100"
4. 插件会自动：
   - 查找并点击下拉菜单触发器
   - 等待下拉菜单展开
   - 查找并点击"100"选项

## 插件特性

### 智能下拉触发器查找
插件使用多种策略查找下拉触发器：
1. 基于`data-testid="beast-core-portal-main"`的容器查找
2. 查找常见的下拉触发器属性（`aria-haspopup`、`aria-expanded`等）
3. 基于当前选中值的元素查找
4. 灵活的DOM结构适配

### 智能选项查找
在下拉菜单展开后：
1. 在`ul[role="listbox"]`中精确查找
2. 基于文本内容("100")的匹配
3. 等待机制确保下拉菜单完全加载
4. 多重尝试机制提高成功率

### 真实交互模拟
- 完整的鼠标事件序列（mouseenter → mouseover → mousedown → mouseup → click）
- 自动滚动到目标元素位置
- 适当的延时确保操作顺序
- 兼容性事件处理（focus、change等）

### 用户友好界面
- 美观的渐变背景设计
- 实时状态反馈（正在处理、成功、失败）
- 按钮禁用状态防止重复点击
- 清晰的成功/失败图标提示

## 故障排除

### 如果插件无法找到下拉触发器：
1. 确保页面已完全加载
2. 检查目标下拉菜单是否存在于页面中
3. 刷新页面后重试
4. 查看浏览器控制台是否有错误信息

### 如果下拉菜单无法打开：
1. 下拉触发器可能被其他元素遮挡
2. 页面可能使用了特殊的事件处理机制
3. 可能需要等待页面JavaScript完全加载

### 如果找不到"100"选项：
1. 下拉菜单可能需要更长时间加载
2. 选项文本可能不是纯"100"（包含空格或其他字符）
3. 下拉菜单结构可能与预期不同

### 调试方法：
1. 按F12打开开发者工具
2. 在Console标签页查看插件日志
3. 检查是否有JavaScript错误

## 技术说明

### 文件结构：
- **manifest.json**: 定义插件权限和配置
- **弹窗.html/弹窗脚本.js**: 用户界面和交互逻辑
- **内容脚本.js**: 在网页中运行，负责查找和点击按钮
- **后台脚本.js**: 后台服务，处理插件生命周期

### 权限说明：
- `activeTab`: 访问当前活动标签页
- `scripting`: 在页面中注入和执行脚本

## 版本信息
- 版本：1.0
- 兼容性：Chrome Manifest V3
- 开发语言：JavaScript, HTML, CSS
