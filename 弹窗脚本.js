document.addEventListener('DOMContentLoaded', function() {
    const clickButton = document.getElementById('clickButton');
    const statusDiv = document.getElementById('status');
    
    // 显示状态信息
    function showStatus(message, isError = false) {
        statusDiv.textContent = message;
        statusDiv.className = isError ? 'status error' : 'status success';
        
        // 3秒后清除状态信息
        setTimeout(() => {
            statusDiv.textContent = '';
            statusDiv.className = 'status';
        }, 3000);
    }
    
    // 点击按钮事件处理
    clickButton.addEventListener('click', async function() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            if (!tab) {
                showStatus('无法获取当前标签页', true);
                return;
            }
            
            // 向内容脚本发送消息
            chrome.tabs.sendMessage(tab.id, {action: 'clickTargetButton'}, function(response) {
                if (chrome.runtime.lastError) {
                    showStatus('连接页面失败，请刷新页面后重试', true);
                    return;
                }
                
                if (response && response.success) {
                    showStatus('按钮点击成功！');
                } else {
                    showStatus(response ? response.message : '未找到目标按钮', true);
                }
            });
            
        } catch (error) {
            console.error('Error:', error);
            showStatus('操作失败：' + error.message, true);
        }
    });
    
    // 检查页面是否已加载内容脚本
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'ping'}, function(response) {
                if (chrome.runtime.lastError) {
                    statusDiv.textContent = '等待页面加载...';
                    statusDiv.className = 'status';
                }
            });
        }
    });
});
